<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScaleUp Marketing</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            color: #333;
            overflow-x: hidden;
            background-color: #fff;
        }
        
        /* Navigation Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 80px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }

        .logo img {
            height: 60px;
            width: auto;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
        }
        
        .nav-links li {
            margin: 0 20px;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            font-size: 16px;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover {
            color: #e40002; /* Primary red color */
        }

        .contact-btn {
            background-color: #e40002; /* Primary red color */
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .contact-btn:hover {
            background-color: #c40002; /* Darker red */
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(228, 0, 2, 0.4);
        }
        
        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(rgba(11, 8, 161, 0.6), rgba(11, 8, 161, 0.7)), url('https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80') center/cover no-repeat;
            display: flex;
            align-items: center;
            padding: 0 80px;
            position: relative;
        }
        
        .hero-content {
            max-width: 650px;
            position: relative;
            z-index: 2;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 30px;
            color: white;
        }
        
        .hero p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 40px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .cta-button {
            background-color: #e40002; /* Primary red color */
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 5px;
            font-weight: 600;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .cta-button:hover {
            background-color: #c40002; /* Darker red */
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(228, 0, 2, 0.4);
        }
        
        .cta-button i {
            font-size: 18px;
        }
        
        /* Services Section */
        .services {
            padding: 100px 80px;
            background-color: #fff;
        }

        .services-container {
            display: flex;
            align-items: center;
            gap: 80px;
        }

        .stats-section {
            flex: 1;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .stat-box {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #e40002;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: #e40002;
            text-transform: uppercase;
            letter-spacing: 1px;
            line-height: 1.2;
        }

        .content-section {
            flex: 1;
        }

        .content-section h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #0b08a1;
            margin-bottom: 30px;
            line-height: 1.2;
        }

        .content-section p {
            font-size: 1rem;
            color: #666;
            line-height: 1.7;
            margin-bottom: 30px;
        }

        .contact-btn-section {
            background-color: #e40002;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            text-transform: uppercase;
        }

        .contact-btn-section:hover {
            background-color: #c40002;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(228, 0, 2, 0.4);
        }
        
        /* About Section */
        .about {
            padding: 100px 80px;
            background-color: #0b08a1;
            color: white;
        }

        .about-container {
            display: flex;
            gap: 80px;
            align-items: flex-start;
        }

        .about-left {
            flex: 1;
            padding-right: 40px;
        }

        .about-left h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            line-height: 1.2;
        }

        .about-right {
            flex: 1.5;
        }

        .services-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .service-card-new {
            background-color: white;
            color: #333;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .service-card-new h3 {
            font-size: 1.2rem;
            font-weight: 700;
            color: #e40002;
            margin-bottom: 10px;
        }

        .service-subtitle {
            font-size: 0.95rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .service-card-new p:last-child {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
            margin: 0;
        }

        .service-card-bottom {
            grid-column: 1 / -1;
            max-width: 50%;
        }
        
        /* Portfolio Section */
        .portfolio {
            padding: 120px 60px;
            background-color: #fff;
        }

        .portfolio-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .portfolio-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #e40002;
            margin-bottom: 10px;
        }

        .portfolio-header p {
            font-size: 1.1rem;
            color: #0b08a1;
            font-weight: 600;
        }

        .portfolio-categories {
            display: grid;
            grid-template-columns: 1fr 1fr;
            margin-bottom: 40px;
            margin-left: -60px;
            margin-right: -60px;
        }

        .category-header {
            text-align: center;
            padding: 12px;
        }

        .category-header:first-child {
            background-color: #333;
            color: white;
        }

        .category-header:last-child {
            background-color: #f5f5f5;
            color: #333;
        }

        .category-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-bottom: 50px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .portfolio-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .portfolio-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .portfolio-image {
            width: 100%;
            height: 300px;
            overflow: hidden;
            position: relative;
        }

        .portfolio-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .portfolio-item:hover .portfolio-image img {
            transform: scale(1.05);
        }

        .portfolio-date {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: rgba(255, 255, 255, 0.95);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 700;
            color: #333;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .portfolio-more {
            text-align: center;
        }

        .more-btn {
            background-color: #e40002;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            text-transform: uppercase;
        }

        .more-btn:hover {
            background-color: #c40002;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(228, 0, 2, 0.4);
        }

        /* Portfolio Responsive Design */
        @media (max-width: 1024px) {
            .portfolio-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 30px;
            }

            .portfolio {
                padding: 80px 40px;
            }

            .portfolio-categories {
                margin-left: -40px;
                margin-right: -40px;
            }
        }

        @media (max-width: 768px) {
            .portfolio-grid {
                grid-template-columns: 1fr;
                gap: 25px;
            }

            .portfolio {
                padding: 60px 20px;
            }

            .portfolio-categories {
                margin-left: -20px;
                margin-right: -20px;
            }

            .portfolio-image {
                height: 250px;
            }

            .category-header {
                padding: 15px;
            }

            .category-header h3 {
                font-size: 1rem;
            }
        }
        
        /* Contact Section */
        .contact {
            padding: 100px 80px;
            background-color: #f8f9fa;
        }

        .contact-container {
            display: flex;
            gap: 80px;
            align-items: flex-start;
        }

        .contact-left {
            flex: 1;
            max-width: 400px;
        }

        .contact-left h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #e40002;
            line-height: 1.2;
            margin-bottom: 30px;
        }

        .contact-left p {
            font-size: 1.1rem;
            color: #0b08a1;
            line-height: 1.6;
            margin-bottom: 40px;
        }

        .contact-info-item {
            font-size: 1rem;
            color: #333;
            line-height: 1.6;
        }

        .contact-info-item strong {
            color: #333;
            font-weight: 600;
        }

        .contact-form {
            flex: 1;
            background-color: #fff;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        }

        .contact-form h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #0b08a1;
            margin-bottom: 30px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-size: 0.9rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            font-family: 'Roboto', sans-serif;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #e40002;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-disclaimer {
            margin: 20px 0;
        }

        .form-disclaimer small {
            font-size: 0.8rem;
            color: #666;
            line-height: 1.4;
        }

        .form-captcha {
            margin: 20px 0;
        }

        .captcha-checkbox {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .captcha-checkbox input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .captcha-checkbox label {
            margin: 0;
            font-size: 0.9rem;
            color: #333;
        }

        .submit-btn {
            background-color: #333;
            color: white;
            border: none;
            padding: 14px 30px;
            border-radius: 5px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .submit-btn:hover {
            background-color: #555;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        /* Footer */
        footer {
            background-color: #f8f9fa;
            color: #333;
            padding: 60px 0 30px;
            text-align: center;
        }

        .footer-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
            margin-bottom: 40px;
        }

        .footer-logo {
            display: flex;
            justify-content: center;
        }

        .footer-logo img {
            height: 80px;
            width: auto;
        }

        .footer-description {
            max-width: 600px;
        }

        .footer-description p {
            color: #333;
            line-height: 1.6;
            font-size: 1rem;
            margin: 0;
        }

        .footer-email p {
            color: #333;
            font-size: 1rem;
            margin: 0;
            font-weight: 500;
        }

        .footer-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 30px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .footer-links {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .footer-links a {
            color: #333;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #e40002;
        }

        .footer-links span {
            color: #666;
        }

        .footer-copyright p {
            color: #333;
            font-size: 0.9rem;
            margin: 0;
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .navbar {
                padding: 15px 40px;
            }
            
            .hero {
                padding: 0 40px;
            }
            
            .hero h1 {
                font-size: 2.8rem;
            }
            
            .services, .about, .testimonials, .contact {
                padding: 80px 40px;
            }
            
            .about-content {
                flex-direction: column;
            }
            
            .contact-container {
                flex-direction: column;
                gap: 40px;
            }

            .contact-left h2 {
                font-size: 2rem;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .form-row .form-group {
                margin-bottom: 20px;
            }
        }
        
        @media (max-width: 768px) {
            .navbar {
                padding: 15px 20px;
            }
            
            .nav-links {
                display: none;
            }
            
            .hero {
                padding: 0 20px;
                text-align: center;
            }
            
            .hero-content {
                max-width: 100%;
            }
            
            .hero h1 {
                font-size: 2.2rem;
            }
            
            .hero p {
                font-size: 1rem;
            }
            
            .services, .about, .testimonials, .contact {
                padding: 60px 20px;
            }
            
            .section-title h2 {
                font-size: 2rem;
            }
            
            .about-text h2 {
                font-size: 2rem;
            }
            
            .stats {
                flex-direction: column;
            }
            
            .footer-content {
                flex-direction: column;
                gap: 30px;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .footer-links {
                justify-content: center;
            }
        }
        
        @media (max-width: 576px) {
            .hero h1 {
                font-size: 1.8rem;
            }
            
            .hero p {
                font-size: 0.9rem;
            }
            
            .cta-button {
                padding: 14px 24px;
                font-size: 16px;
            }
            
            .section-title h2 {
                font-size: 1.8rem;
            }
            
            .about-text h2 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="logo">
            <img src="Pictures/logo.jpg" alt="ScaleUp Marketing">
        </div>
        <ul class="nav-links">
            <li><a href="#home">HOME</a></li>
            <li><a href="#about">ABOUT US</a></li>
            <li><a href="#services">SERVICES</a></li>
            <li><a href="#testimonials">PORTFOLIO</a></li>
        </ul>
        <button class="contact-btn">CONTACT</button>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>ScaleUp Your Growth, Elevate Your Success</h1>
            <p>We provide sophisticated, data-driven strategies to enhance your online presence, drive targeted traffic, and generate high-quality leads. Whether you're an emerging startup or an established enterprise, our expertise guarantees measurable growth and long-lasting success.</p>
            <button class="cta-button">
                GET IN TOUCH
                <i class="fas fa-arrow-right"></i>
            </button>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="services-container">
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-box">
                        <div class="stat-number">200+</div>
                        <div class="stat-label">CLIENTS</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number">8</div>
                        <div class="stat-label">DIFFERENT<br>COUNTRIES</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number">150+</div>
                        <div class="stat-label">CUSTOM WEBSITES</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number">80,000+</div>
                        <div class="stat-label">WORKING HOURS</div>
                    </div>
                </div>
            </div>
            <div class="content-section">
                <h2>Let's Scale Together! Our Strategies, Your Success</h2>
                <p>ScaleUp Marketing provides strategic digital marketing solutions that fuel business growth. Our experienced team is committed to developing customized strategies that enhance brand visibility, effectively engage audiences, and maximize return on investment. With a deep understanding of industry trends and access to cutting-edge tools, we empower businesses to maintain a competitive edge in the constantly evolving digital landscape.</p>
                <button class="contact-btn-section">CONTACT</button>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="about" id="about">
        <div class="about-container">
            <div class="about-left">
                <h2>ScaleUp your online presence, Grow your sales with our expert strategies.</h2>
            </div>
            <div class="about-right">
                <div class="services-cards">
                    <div class="service-card-new">
                        <h3>Website Design & Development</h3>
                        <p class="service-subtitle">Your website acts as a digital gateway to your brand.</p>
                        <p>We create visually appealing, user-friendly, and fully responsive websites that offer seamless navigation and an outstanding user experience, ensuring maximum engagement and conversions.</p>
                    </div>
                    <div class="service-card-new">
                        <h3>PPC (Pay-Per-Click)</h3>
                        <p class="service-subtitle">Enhance your advertising budget with precisely targeted PPC campaigns.</p>
                        <p>We develop and manage Google Ads and social media advertising strategies aimed at attracting high-intent traffic, ensuring effective lead generation and cost efficiency.</p>
                    </div>
                    <div class="service-card-new">
                        <h3>Social Media Marketing</h3>
                        <p class="service-subtitle">Build a strong presence on social media through effective campaigns.</p>
                        <p>We utilize data-driven strategies to enhance brand engagement, grow your audience, and create meaningful interactions via engaging content and targeted advertising.</p>
                    </div>
                    <div class="service-card-new">
                        <h3>SEO (Search Engine Optimization)</h3>
                        <p class="service-subtitle">Enhance your search engine rankings and broaden your online presence.</p>
                        <p>Our SEO experts use best practices to optimize your website, improve keyword relevance, and enrich content, ultimately boosting organic traffic and visibility.</p>
                    </div>
                    <div class="service-card-new service-card-bottom">
                        <h3>Email Marketing</h3>
                        <p class="service-subtitle">Strengthen customer relationships and increase conversions through effective email campaigns.</p>
                        <p>Our approach includes personalized messaging, automation, and targeted content delivery to engage your audience effectively and drive measurable results.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section class="portfolio" id="portfolio">
        <div class="portfolio-header">
            <h2>Portfolio</h2>
            <p>Explore Our Work</p>
        </div>

        <div class="portfolio-categories">
            <div class="category-header">
                <h3>Web Design & Development</h3>
            </div>
            <div class="category-header">
                <h3>Digital Marketing</h3>
            </div>
        </div>

        <div class="portfolio-grid">
            <!-- Web Design & Development Projects -->
            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="Pictures/Maher-Builders.png" alt="Project 1">
                </div>
                <div class="portfolio-date">AUG / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="Pictures/Abu-Emp.png" alt="Project 2">
                </div>
                <div class="portfolio-date">AUG / 2024</div>
            </div>

            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="Pictures/Gunya-Spa.png" alt="Project 3">
                </div>
                <div class="portfolio-date">MAY / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="Pictures/Beyond-Beauty.png" alt="Project 4">
                </div>
                <div class="portfolio-date">MAY / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="Pictures/UPRGI.png" alt="Project 5">
                </div>
                <div class="portfolio-date">MAY / 2024</div>
            </div>

            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="Pictures/ICSB.png" alt="Project 6">
                </div>
                <div class="portfolio-date">MAY / 2024</div>
            </div>

            <!-- Row 3 -->
            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="Pictures/Kapsule.png" alt="Project 7">
                </div>
                <div class="portfolio-date">APR / 2024</div>
            </div>

            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="Pictures/Fgen.png" alt="Project 8">
                </div>
                <div class="portfolio-date">APR / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="Pictures/Automarket.png" alt="Project 9">
                </div>
                <div class="portfolio-date">APR / 2024</div>
            </div>

            <!-- Row 4 -->
            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="Pictures/ANPI-B.png" alt="Project 10">
                </div>
                <div class="portfolio-date">MAR / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="Pictures/MMCLaw.png" alt="Project 11">
                </div>
                <div class="portfolio-date">MAR / 2024</div>
            </div>

            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="Pictures/Straight-Arrow.png" alt="Project 12">
                </div>
                <div class="portfolio-date">MAR / 2024</div>
            </div>


        </div>

        <div class="portfolio-more">
            <button class="more-btn">More</button>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="contact-container">
            <div class="contact-left">
                <h2>Let us elevate your business to new heights.</h2>
                <p>Begin your journey towards transformative growth today. Whether you need a comprehensive digital marketing strategy or specialized services, we are here to help.</p>
                <div class="contact-info-item">
                    <strong>Email</strong><br>
                    <EMAIL>
                </div>
            </div>
            <div class="contact-form">
                <h3>We'd love to hear from you. Let's chat.</h3>
                <form>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">First Name</label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name</label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="contactNumber">Contact Number</label>
                            <input type="tel" id="contactNumber" name="contactNumber">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="businessName">Business Name</label>
                        <input type="text" id="businessName" name="businessName">
                    </div>
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject">
                    </div>
                    <div class="form-group">
                        <label for="message">Message</label>
                        <textarea id="message" name="message" rows="5"></textarea>
                    </div>
                    <div class="form-disclaimer">
                        <small>By providing a telephone number and submitting this form you are consenting to be contacted by SMS text message. Message & data rates may apply. You can reply STOP to opt-out of further messaging.</small>
                    </div>
                    <div class="form-captcha">
                        <div class="captcha-checkbox">
                            <input type="checkbox" id="notRobot" name="notRobot" required>
                            <label for="notRobot">I'm not a robot</label>
                        </div>
                    </div>
                    <button type="submit" class="submit-btn">SUBMIT</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="Pictures/logo.jpg" alt="ScaleUp Marketing">
                </div>
                <div class="footer-description">
                    <p>At ScaleUp Marketing, we specialize in data-driven digital marketing strategies that boost your online presence, enhance brand visibility, and drive business growth. Our mission is to provide digital solutions that resonate with our client's audience and set them apart from the competition.</p>
                </div>
                <div class="footer-email">
                    <p><EMAIL></p>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-links">
                    <a href="#">Privacy Policy</a>
                    <span>—</span>
                    <a href="#">Terms of Use</a>
                </div>
                <div class="footer-copyright">
                    <p>© 2025 ScaleUp Marketing. Powered by ScaleUp7</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.padding = '15px 80px';
                navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.15)';
            } else {
                navbar.style.padding = '20px 80px';
                navbar.style.backgroundColor = '#fff';
                navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Form submission
        document.querySelector('.contact-form form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thank you for your message! We will get back to you soon.');
            this.reset();
        });

        // Counter Animation
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16); // 60fps
            const timer = setInterval(() => {
                start += increment;
                if (start >= target) {
                    element.textContent = target.toString().includes('.') ? target.toFixed(1) : Math.floor(target);
                    if (target >= 1000) {
                        element.textContent = element.textContent.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                    }
                    // Add the + sign back if it was in the original
                    if (element.dataset.original && element.dataset.original.includes('+')) {
                        element.textContent += '+';
                    }
                    clearInterval(timer);
                } else {
                    let currentValue = Math.floor(start);
                    if (currentValue >= 1000) {
                        element.textContent = currentValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                    } else {
                        element.textContent = currentValue;
                    }
                    // Add the + sign for display during animation if original had it
                    if (element.dataset.original && element.dataset.original.includes('+')) {
                        element.textContent += '+';
                    }
                }
            }, 16);
        }

        // Intersection Observer for counter animation
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = entry.target.querySelectorAll('.stat-number');
                    statNumbers.forEach(stat => {
                        if (!stat.classList.contains('animated')) {
                            stat.classList.add('animated');
                            // Store original text
                            stat.dataset.original = stat.textContent;
                            // Extract number from text (remove + and commas)
                            const targetValue = parseInt(stat.textContent.replace(/[+,]/g, ''));
                            // Start animation
                            animateCounter(stat, targetValue);
                        }
                    });
                }
            });
        }, observerOptions);

        // Observe the services section
        const servicesSection = document.querySelector('.services');
        if (servicesSection) {
            observer.observe(servicesSection);
        }

        // Portfolio item click functionality
        document.addEventListener('DOMContentLoaded', function() {
            const portfolioItems = document.querySelectorAll('.portfolio-item');

            portfolioItems.forEach(function(item, index) {
                item.addEventListener('click', function() {
                    const projectNumber = index + 1;
                    const category = item.classList.contains('web-design') ? 'Web Design & Development' : 'Digital Marketing';
                    const date = item.querySelector('.portfolio-date').textContent;

                    // Create modal or alert with project details
                    const projectDetails = `
Project ${projectNumber}
Category: ${category}
Date: ${date}

This is a sample project description. In a real implementation, you would:
- Show a detailed project gallery
- Display project description and objectives
- List technologies used
- Show client testimonials
- Include project results and metrics
                    `;

                    alert(projectDetails);

                    // Alternative: You could redirect to a detailed project page
                    // window.location.href = `/portfolio/project-${projectNumber}`;
                });
            });
        });
    </script>
</body>
</html>